<?php
	session_start();
	include_once("../database/repair.class.php");
	$obj = new repairClass();
	
	if($_POST['ajax'] == "GetCustomPalletDetails") {
		$result = $obj->GetCustomPalletDetails($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetmpnDetails") {
		$result = $obj->GetmpnDetails($_POST);
		echo $result;
	}
	
	if($_POST['ajax'] == "GetMPNFromSerialrepair") {
		$result = $obj->GetMPNFromSerialrepair($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetmpnmediaDetails") {
		$result = $obj->GetmpnmediaDetails($_POST);
		echo $result;
	}
	
	if($_POST['ajax'] == "UpdateAssetrepair") {
		$result = $obj->UpdateAssetrepair($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetrepairAssets") {
		$result = $obj->GetrepairAssets($_POST);
		echo $result;
	}

	// Actions Functions for Bin Management
	if($_POST['ajax'] == "CreateBin") {
		$result = $obj->CreateBin($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "CloseBin") {
		$result = $obj->CloseBin($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "NestToBin") {
		$result = $obj->NestToBin($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetAvailableParentBins") {
		$result = $obj->GetAvailableParentBins($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetSessionFacility") {
		$result = $obj->GetSessionFacility($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetBinPackageTypes") {
		$result = $obj->GetBinPackageTypes($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetStationLocationGroup") {
		$result = $obj->GetStationLocationGroup($_POST);
		echo $result;
	}
?>