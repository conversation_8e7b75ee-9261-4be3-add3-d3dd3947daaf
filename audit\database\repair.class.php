<?php
session_start();
include_once("audit.class.php");
class repairClass extends AuditClass {
    
    public function GetCustomPalletDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'repair')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to repair Page';
				return json_encode($json);
			}

			//Start Delete all locked source bins for logged in user for this workflow
			$query6 = "delete from source_bin_user_mapping where CreatedBy = '".$_SESSION['user']['UserId']."' and workflow_id = '7'";
			$q6 = mysqli_query($this->connectionlink,$query6);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Delete all locked source bins for logged in user for this workflow


			$query = "select c.*,s.Status from custompallet c 
			left join custompallet_status s on c.StatusID = s.StatusID 
			where c.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['BinName'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					//$json['Result'] = 'BIN Status is not Active';
					$json['Result'] = 'BIN Status is '.$row['Status'];
					return json_encode($json);
				}

				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = 'BIN Facility is different from Users Facility';
					return json_encode($json);
				}

				//Start check IF BIN is locked for Source BIN
				$query4 = "select count(*) from source_bin_user_mapping where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."' ";
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row4 = mysqli_fetch_assoc($q4);
					if($row4['count(*)'] > 0) {
						$json['Success'] = false;
						$json['Result'] = 'BIN is locked as Source BIN';
						return json_encode($json);
					}
				}
				//End check IF BIN is locked for Source BIN

				//Start check IF Bin Disposition is mapped for the workflow or not
				$query1 = "select count(*) from workflow_disposition where disposition_id = '".$row['disposition_id']."' and workflow_id = '7'";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					if($row1['count(*)'] == 0) {
						$json['Success'] = false;
						$json['Result'] = 'BIN Disposition not mapped to Workflow';
						return json_encode($json);
					}
				}
				//End check IF Bin Disposition is mapped for the workflow or not

				//Start check If Source and any of the Destinations Bins are Same
				//if($data['SiteID'] > 0) {
				if(true) {
					//$query2 = "select m.*,d.disposition from station_custompallet_mapping m left join disposition d on m.disposition_id = d.disposition_id 
					//where m.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."' ";

					$query2 = "select m.*,s.SiteName from station_custompallet_mapping m left join site s on m.SiteID = s.SiteID 
					where m.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."' ";

					$q2 = mysqli_query($this->connectionlink,$query2);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row2 = mysqli_fetch_assoc($q2);
						$json['Success'] = false;
						$json['Result'] = 'BIN is defined as Output BIN for Station '.$row2['SiteName'];
						return json_encode($json);
					}
				}
				//End check If Source and any of the Destinations Bins are Same

				//Start Insert into Source Locking
				// $query3 = "insert into source_bin_user_mapping (CustomPalletID,workflow_id,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."',7,NOW(),'".$_SESSION['user']['UserId']."')";
				// $q3 = mysqli_query($this->connectionlink,$query3);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				//End Insert into Source Locking

				$json['Success'] = true;
				$json['CustomPalletID'] = $row['CustomPalletID'];
			} else {
				$json['Success'] = false;
				$json['Result'] = "Invalid BIN";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	 public function GetmpnDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'repair')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to repair Page';
				return json_encode($json);
			}

			$query = "select * from inventory where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['CustomPalletID'] != $data['CustomPalletID']) {
					$json['Success'] = false;
					$json['Result'] = "Serial Number not available in the Selected BIN";
					return json_encode($json);
				}
				if($row['InventoryStatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "Serial Number Status is not Active";
					return json_encode($json);
				}
				$json['Success'] = true;
				$json['Result'] = $row;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Serial Number details not available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetmpnmediaDetails ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'repair')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to repair Page';
				return json_encode($json);
			}
			//$query = "select * from inventory where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";
			$query = "select a.*,d.disposition from asset a 
			left join disposition d on a.disposition_id = d.disposition_id 
			where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."'";

			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = "Serial Number Status is not Active";
					return json_encode($json);
				}

				if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
					$json['Success'] = false;
					$json['Result'] = "Serial Number Facility is different from Current Facility";
					return json_encode($json);
				}
				if($row['disposition'] != 'Temporary-RepairConsumable') {
					$json['Success'] = false;
					$json['Result'] = "Serial Number Disposition is not Temporary-RepairConsumable	";
					return json_encode($json);
				}

				$json['Success'] = true;
				$json['Result'] = $row;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Serial Number details not available";
				return json_encode($json);
			}			

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetMPNFromSerialrepair ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'repair')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to repair Page';
				return json_encode($json);
			}

			// $query = "select a.*,m.RepairType from asset a 
			// left join catlog_creation m on a.UniversalModelNumber = m.mpn_id 
			// where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and a.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."' order by StatusID";

			$query = "select a.*,m.RepairType from asset a 
			left join catlog_creation m on a.UniversalModelNumber = m.mpn_id 
			where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' and a.FacilityID = '".$_SESSION['user']['FacilityID']."' order by StatusID ";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {	
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				// if($row['CustomPalletID'] != $data['CustomPalletID']) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = "Serial Number not available in the Selected BIN";
				// 	return json_encode($json);
				// }
				if($row['StatusID'] != '1' && $row['StatusID'] != '9') {
					$json['Success'] = false;
					$json['Result'] = "Serial Number Status is not Active";
					return json_encode($json);
				}

				//Start get Repair type value file
				if($row['RepairType'] != '') {
					$query1 = "select file_url from business_rule_attribute_values where attribute_id = '22' and value = '".mysqli_real_escape_string($this->connectionlink,$row['RepairType'])."'";
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {	
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row1 = mysqli_fetch_assoc($q1);
						$row['file_url'] = $row1['file_url'];
					} else {
						$row['file_url'] = '';
					}
				} else {
					$row['file_url'] = '';
				}			
				//End get Repair type value file


				//Start get Default part type and evaluation result for the subcomponent
				$query4 = "select parttypeid,input_id from recover_configuration_sub_component where DefaultValue = '1' and FacilityID = '".$_SESSION['user']['FacilityID']."' and FromDispositionID = '".$row['disposition_id']."' and workflow_id = '7' ";//Workflow_id for repair is 7
				$q4 = mysqli_query($this->connectionlink,$query4);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row4 = mysqli_fetch_assoc($q4);
					$row['media_parttypeid'] = $row4['parttypeid'];
					$row['media_input_id'] = $row4['input_id'];
				} else {
					$row['media_parttypeid'] = '';
					$row['media_input_id'] = '';
				}
				//End get Default part type and evaluation result for the subcomponent

				$json['Success'] = true;
				$json['Result'] = $row;
				return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = "Serial Number details not available";
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function UpdateAssetrepair ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => $data
		);	
		//return json_encode($json);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Repair')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Repair Page';
				return json_encode($json);
			}
			if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Repair')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Repair Page';
				return json_encode($json);
			}

			if($data['SerialNumber'] == '') {
				$json['Success'] = false;
				$json['Result'] = 'Serial Number is required';
				return json_encode($json);
			}

			//Start validate MPN
			$mpn_validate= $this->ValidateMPN($data['UniversalModelNumber']);
			if($mpn_validate['Success']) {              
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid MPN';
				return json_encode($json);
			}
			//End validate MPN

			//Start get asset Details
			//$query20 = "select * from asset where SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' order by StatusID";
			$query20 = "select a.*,cp.BinName from asset a 
			left join custompallet cp on a.CustomPalletID = cp.CustomPalletID 
			where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' order by StatusID";
			$q20 = mysqli_query($this->connectionlink,$query20);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$old = mysqli_fetch_assoc($q20);
				// if($old['CustomPalletID'] != $data['FromCustomPalletID']) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = 'Serial Bin is different from Source BIN';
				// 	return json_encode($json);
				// }
				if($old['StatusID'] != 1 && $old['StatusID'] != '9') {
					$json['Success'] = false;
					$json['Result'] = 'Asset Status is not Active';
					return json_encode($json);
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Serial';
				return json_encode($json);
			}
			//End get asset Details



			//Start validate Custom pallet
			$query = "Select MaximumAssets,AssetsCount,StatusID,BinName from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row = mysqli_fetch_assoc($q);
				if($row['StatusID'] != '1') {
					$json['Success'] = false;
					$json['Result'] = 'BIN Status is not Active';
					return json_encode($json);	
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid BIN';
				return json_encode($json);
			}			
			//End validate Custom pallet


			//Start validate From Custom pallet
			// $query20 = "Select MaximumAssets,AssetsCount,StatusID,BinName from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."'";
			// $q20 = mysqli_query($this->connectionlink,$query20);
			// if(mysqli_error($this->connectionlink)) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = mysqli_error($this->connectionlink);
			// 	return json_encode($json);
			// }
			// if(mysqli_affected_rows($this->connectionlink) > 0) {
			// 	$FromCP = mysqli_fetch_assoc($q20);
			// 	if($FromCP['StatusID'] != '1') {
			// 		$json['Success'] = false;
			// 		$json['Result'] = 'Source BIN Status is not Active';
			// 		return json_encode($json);	
			// 	}
			// } else {
			// 	$json['Success'] = false;
			// 	$json['Result'] = 'Invalid Source BIN';
			// 	return json_encode($json);
			// }			
			//End validate From Custom pallet

			
			//Start Validate media details
			for($i=0;$i<count($data['repairMedia']);$i++) {
                if($data['repairMedia'][$i]['repair_out_sn'] != '' && $data['repairMedia'][$i]['repair_out_sn'] != 'n/a') {
					// if($data['repairMedia'][$i]['InventoryID'] > 0) {
					// } else {
					// 	$json['Success'] = false;
					// 	$json['Result'] = "Media IN SN at row ".intval($i+1) ." is invalid";
                    //     return json_encode($json);
					// }
					$validate_sub_component_serial = $this->ValidateInventorySerial1($data['repairMedia'][$i]['repair_out_sn']);
					if($validate_sub_component_serial['Success']) {                        
                    } else {
                        $json['Success'] = false;
                        $json['Result'] = 'Media OUT SN ('.$data['repairMedia'][$i]['repair_out_sn'].') '.$validate_sub_component_serial['Error'];
                        return json_encode($json);
                    }

                    //$validate_bin = $this->ValidateInventoryBinName($data['repairMedia'][$i]['repair_bin_id'],$data['sub_disposition_id']);
					$validate_bin = $this->ValidateInventoryBinName($data['repairMedia'][$i]['repair_bin_id'],$data['repairMedia'][$i]['sub_disposition_id']);
                    if($validate_bin['Success']) {                        
                        $data['repairMedia'][$i]['CustomPalletID'] = $validate_bin['CustomPalletID'];
                        $data['repairMedia'][$i]['BinName'] = $validate_bin['BinName'];                        
                    } else {
                        $json['Success'] = false;
                        $json['Result'] = 'Repair BIN ('.$data['repairMedia'][$i]['repair_bin_id'].') '.$validate_bin['Error'];
                        return json_encode($json);
                    }   
					
					$validate_mpn= $this->ValidateMPN($data['repairMedia'][$i]['repair_out_mpn']);
					if($validate_mpn['Success']) { 
						
						$mpn =  $validate_mpn['MPN'];
						
						$query1 = "select parttype from parttype where parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['parttypeid'])."'";
						$q1 = mysqli_query($this->connectionlink,$query1);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}
						if(mysqli_affected_rows($this->connectionlink) > 0) {
							$row1 = mysqli_fetch_assoc($q1);

							$PartType = $row1['parttype'];		
							if($PartType != $mpn['part_type']) {
								$json['Success'] = false;
								$json['Result'] = 'Media OUT MPN ('.$data['repairMedia'][$i]['repair_out_mpn'].') and PartType ('.$PartType.') combination is not valid';
								return json_encode($json);
							}
						} else {
							$json['Success'] = false;
							$json['Result'] = 'Invalid Part Type';
							return json_encode($json);
						}
						
                    } else {
                        $json['Success'] = false;
                        $json['Result'] = 'Repair OUT MPN ('.$data['repairMedia'][$i]['repair_out_mpn'].') '.$validate_mpn['Error'];
                        return json_encode($json);
                    }
                    
                    //Start validate Serial Number
                    //End Validate Serial Number
                }				
			}
			//End Validate media details



			//Start get Part Type from parttypeid
			$query1 = "select parttype from parttype where parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['parttypeid'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$row1 = mysqli_fetch_assoc($q1);

				$PartType = $row1['parttype'];										
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid Part Type';
				return json_encode($json);
			}

			$MPNPartTypeQ = "select part_type,apn_id,idManufacturer from catlog_creation where mpn_id='".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and lower(part_type)=lower('".mysqli_real_escape_string($this->connectionlink,$PartType)."') and FacilityID = '".$_SESSION['user']['FacilityID']."'";
			$MPNPartTypeQEx = mysqli_query($this->connectionlink,$MPNPartTypeQ);
			if(mysqli_affected_rows($this->connectionlink)==0){
				$json = array(
					'Success' => false,
					'Result' => "The combination of MPN and Part Type doesn't exist"
				);
				return json_encode($json);
			} else {
				$mpn = mysqli_fetch_assoc($MPNPartTypeQEx);
			}


			if($data['repair_id']) { // Update existins

			} else {
				$data['OriSerialNumber'] = $data['SerialNumber'];
				//$data['SerialNumber'] = preg_replace('/[^A-Za-z0-9]/', '', $data['SerialNumber']);
				if(!$data['result_scan_time'] || $data['result_scan_time'] == '') {
					$data['result_scan_time'] = $data['serial_scan_time'];
				}
				$event_id = rand(1000000000, 9999999999);
				$batch_event_flag = 'N';
				$query1 = "insert into asset_repair (AssetScanID,SerialNumber,UniversalModelNumber,FromCustomPalletID,FromBinName,ToCustomPalletID,ToBinName,repair_input_id,repair_type,repair_verification_id,repair_seal_id,repair_controller_login_id,repair_custom_id,repair_notes,repair_rule_id,disposition_id,CreatedDate,CreatedBy,SiteID,ActualSerialNumber,workstation_scan_time,serial_scan_time,mpn_scan_time,result_scan_time,bin_scan_time,event_id,batch_event_flag,parttypeid,COOID,part_type_scan_time,coo_scan_time) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$old['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$old['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$row['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['repair_input_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['RepairType'])."','".mysqli_real_escape_string($this->connectionlink,$data['repair_verification_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['repair_seal_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['repair_controller_login_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['repair_custom_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['repair_notes'])."','".mysqli_real_escape_string($this->connectionlink,$data['repair_rule_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".$data['OriSerialNumber']."','".mysqli_real_escape_string($this->connectionlink,$data['workstation_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['mpn_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['result_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['bin_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$event_id)."','".mysqli_real_escape_string($this->connectionlink,$batch_event_flag)."','".mysqli_real_escape_string($this->connectionlink,$data['parttypeid'])."','".mysqli_real_escape_string($this->connectionlink,$data['COOID'])."','".mysqli_real_escape_string($this->connectionlink,$data['part_type_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['coo_scan_time'])."')";
				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				$id = mysqli_insert_id($this->connectionlink);
			}
			
			//Start update Asset
			$query1 = "update asset set UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."',CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',RecentWorkflowID = '7',RecentWorkflowDate = NOW(),RecentWorkflowBy = '".$_SESSION['user']['UserId']."',part_type = '".mysqli_real_escape_string($this->connectionlink,$PartType)."',parttypeid = '".mysqli_real_escape_string($this->connectionlink,$data['parttypeid'])."',COOID = '".mysqli_real_escape_string($this->connectionlink,$data['COOID'])."',idManufacturer = '".mysqli_real_escape_string($this->connectionlink,$mpn['idManufacturer'])."',apn_id = '".mysqli_real_escape_string($this->connectionlink,$mpn['apn_id'])."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
			$q1 = mysqli_query($this->connectionlink,$query1);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}			
			//End update Asset

			if($old['FirstRepairCustomPalletID'] > 0) {

			} else {//First time Repair screen
				$query102 = "update asset set FirstRepairCustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',FirstRepairDateTime = NOW(),FirstRepairBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
				$q102 = mysqli_query($this->connectionlink,$query102);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			}

			//Start update Custom Pallet Items
			$query2 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
			$q2 = mysqli_query($this->connectionlink,$query2);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Items

			//Start update Custom Pallet Counts
			$query3 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$query4 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$old['CustomPalletID'])."'";
			$q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End update Custom Pallet Counts

			//Insert into Asset Tracking			
			// $query2 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','Asset Processed in Repair Screen','','',NOW(),'".$_SESSION['user']['UserId']."')";
			// $q2 = mysqli_query($this->connectionlink,$query2);
			// if(mysqli_error($this->connectionlink)) {
			// 	$json['Success'] = false;
			// 	$json['Result'] = mysqli_error($this->connectionlink);
			// 	return json_encode($json);
			// }

			$desc = "Asset Processed in Repair Screen and moved to BIN, (BIN ID : ".$row['BinName'].")";
			$query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			//End Inserting into Asset Tracking

			//Start check If MPN is changed
			if($data['UniversalModelNumber'] != $old['UniversalModelNumber']) {
				$desc = "Asset MPN Changed from '".$old['UniversalModelNumber']."' to '".$data['UniversalModelNumber']."' in repair Screen";
				$query4 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
				$q4 = mysqli_query($this->connectionlink,$query4);


				//Start get APN from MPN
				// $query191 = "select apn_id,part_type,idManufacturer from catlog_creation where mpn_id = '".mysqli_real_escape_string($this->connectionlink,$data['UniversalModelNumber'])."' and FacilityID = '".$_SESSION['user']['FacilityID']."' ";
				// $q191 = mysqli_query($this->connectionlink,$query191);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				// if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 	$row191 = mysqli_fetch_assoc($q191);
				// 	$APN = $row191['apn_id'];
				// 	$PART_TYPE = $row191['part_type'];
				// 	$ID_MANU = $row191['idManufacturer'];
				// } else {
				// 	$APN = '';
				// 	$PART_TYPE = '';
				// 	$ID_MANU = '';
				// }
				// $query192 = "update asset set apn_id = '".mysqli_real_escape_string($this->connectionlink,$APN)."',part_type = '".mysqli_real_escape_string($this->connectionlink,$PART_TYPE)."',idManufacturer = '".mysqli_real_escape_string($this->connectionlink,$ID_MANU)."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
				// $q192 = mysqli_query($this->connectionlink,$query192);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				//End get APN from MPN

			}			
			//End check IF MPN is changed


			//Start check If Disposition is changed
			if($data['disposition_id'] != $old['disposition_id']) {
				$desc = "Asset Disposition Changed in repair Screen";
				if($data['repair_rule_id'] > 0) {
					//Write code for getting rule_id_text from business_rule table and add the rule_id_text to the description
					$query101 = "select rule_id_text from business_rule where rule_id = '".mysqli_real_escape_string($this->connectionlink,$data['repair_rule_id'])."'";
					$q101 = mysqli_query($this->connectionlink,$query101);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						$row101 = mysqli_fetch_assoc($q101);
						$desc = $desc . " (Business Rule ID : ".$row101['rule_id_text'].")";
					}
				}
				$query5 = "insert into asset_tracking (AssetScanID,`Action`,Description,UniqueID,CreatedDate,CreatedBy,`Table`,ReferenceID,RequestName) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".$desc."','','".mysqli_real_escape_string($this->connectionlink,$data['disposition_id'])."',NOW(),'".$_SESSION['user']['UserId']."','disposition','disposition_id','disposition')";
				$q5 = mysqli_query($this->connectionlink,$query5);

				$query5 = "update asset set RecentDispositionDate = NOW(),RecentDispositionRuleID = '".mysqli_real_escape_string($this->connectionlink,$data['repair_rule_id'])."',RecentDispositionBy = '".$_SESSION['user']['UserId']."',RecentDispositionComments = 'Updated in Repair page' where AssetScanID =  '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
				$q5 = mysqli_query($this->connectionlink,$query5);

			}			
			//End check IF Disposition is changed
			
			//Start insert media details
			if(count($data['repairMedia']) > 1) {
				$batch_event_flag = 'Y';
			} else {
				$batch_event_flag = 'N';
			}
			for($i=0;$i<count($data['repairMedia']);$i++) {
				if($data['repairMedia'][$i]['repair_out_sn'] != '' && $data['repairMedia'][$i]['repair_out_sn'] != 'n/a') {
					//Start create asset
					//$HarvestAssetScanID = $this->GetRandom();
					$data['OriSerialNumber'] = $data['repairMedia'][$i]['repair_out_sn'];
					$data['repairMedia'][$i]['repair_out_sn'] = preg_replace('/[^A-Za-z0-9]/', '', $data['repairMedia'][$i]['repair_out_sn']);
					//$query12 = "insert into asset (AssetScanID,idPallet,SerialNumber,Quantity,FacilityID,DateCreated,CreatedBy,StatusID,AccountID,CustomPalletID,UniversalModelNumber,SiteID,AssetLevel,ParentAssetScanID) values ('".mysqli_real_escape_string($this->connectionlink,$HarvestAssetScanID)."','".mysqli_real_escape_string($this->connectionlink,$old['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$data['HarvestMedia'][$i]['harvest_sn'])."','1','".$_SESSION['user']['FacilityID']."',NOW(),'".$_SESSION['user']['UserId']."','1','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['HarvestMedia'][$i]['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['HarvestMedia'][$i]['harvest_mpn'])."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".mysqli_real_escape_string($this->connectionlink,$old['AssetLevel'] + 1)."','".mysqli_real_escape_string($this->connectionlink,$old['AssetScanID'])."')";
					// $query12 = "insert into inventory (SerialNumber,CreatedDate,CreatedBy,InventoryStatusID,CustomPalletID,UniversalModelNumber,ParentAssetScanID,ParentAssetSerialNumber,InventoryType,ActualSerialNumber,recovered_serial_scan_time,recovered_mpn_scan_time,event_id,batch_event_flag,parttypeid,COOID,input_id";
					// if($data['sub_disposition_id'] > 0) {
					// 	$query12 = $query12 .",disposition_id";	
					// }
					// $query12 = $query12.") values ('".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_out_sn'])."',NOW(),'".$_SESSION['user']['UserId']."','1','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_out_mpn'])."','".mysqli_real_escape_string($this->connectionlink,$old['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$old['SerialNumber'])."','Repair','".$data['OriSerialNumber']."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['recovered_serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['recovered_mpn_scan_time'])."','".$event_id."','".$batch_event_flag."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['parttypeid'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['COOID'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['input_id'])."'";
					// if($data['sub_disposition_id'] > 0) {
					// 	$query12 = $query12 .",'".mysqli_real_escape_string($this->connectionlink,$data['sub_disposition_id'])."'";	
					// }
					// $query12 = $query12.")";
					
					$HarvestAssetScanID = $this->GetRandom();
					if($old['idPallet'] != '') {
						$query12 = "insert into asset (AssetScanID,idPallet,SerialNumber,Quantity,FacilityID,DateCreated,CreatedBy,StatusID,AccountID,CustomPalletID,UniversalModelNumber,SiteID,AssetLevel,ParentAssetScanID,InventoryType,serial_scan_time,mpn_scan_time,event_id,batch_event_flag,parttypeid,COOID,input_id,disposition_id,part_type,TopLevelSerial,TopLevelAssetScanID,part_type_scan_time,result_scan_time,coo_scan_time,bin_scan_time) values ('".mysqli_real_escape_string($this->connectionlink,$HarvestAssetScanID)."','".mysqli_real_escape_string($this->connectionlink,$old['idPallet'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_out_sn'])."','1','".$_SESSION['user']['FacilityID']."',NOW(),'".$_SESSION['user']['UserId']."','13','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_out_mpn'])."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".mysqli_real_escape_string($this->connectionlink,$old['AssetLevel'] + 1)."','".mysqli_real_escape_string($this->connectionlink,$old['AssetScanID'])."','Repair','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['recovered_serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['recovered_mpn_scan_time'])."','".$event_id."','".$batch_event_flag."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['parttypeid'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['COOID'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['input_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['sub_disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$old['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$old['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['part_type_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['result_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['coo_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['bin_scan_time'])."')";
					} else {
						$query12 = "insert into asset (AssetScanID,SerialNumber,Quantity,FacilityID,DateCreated,CreatedBy,StatusID,AccountID,CustomPalletID,UniversalModelNumber,SiteID,AssetLevel,ParentAssetScanID,InventoryType,serial_scan_time,mpn_scan_time,event_id,batch_event_flag,parttypeid,COOID,input_id,disposition_id,part_type,TopLevelSerial,TopLevelAssetScanID,part_type_scan_time,result_scan_time,coo_scan_time,bin_scan_time) values ('".mysqli_real_escape_string($this->connectionlink,$HarvestAssetScanID)."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_out_sn'])."','1','".$_SESSION['user']['FacilityID']."',NOW(),'".$_SESSION['user']['UserId']."','13','".$_SESSION['user']['AccountID']."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_out_mpn'])."','".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."','".mysqli_real_escape_string($this->connectionlink,$old['AssetLevel'] + 1)."','".mysqli_real_escape_string($this->connectionlink,$old['AssetScanID'])."','Repair','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['recovered_serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['recovered_mpn_scan_time'])."','".$event_id."','".$batch_event_flag."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['parttypeid'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['COOID'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['input_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['sub_disposition_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$old['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$old['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['part_type_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['result_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['coo_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['bin_scan_time'])."')";
					}					
					$q12 = mysqli_query($this->connectionlink,$query12);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					$inventory_id = mysqli_insert_id($this->connectionlink);
					//Start inserting into custompallet
					//$query13 = "insert into custompallet_items (InventoryID,DateCreated,status,CreatedBy,CustomPalletID) values ('".mysqli_real_escape_string($this->connectionlink,$inventory_id)."',NOW(),'1','".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['CustomPalletID'])."')";
					$query13 = "insert into custompallet_items (AssetScanID,DateCreated,status,CreatedBy,CustomPalletID) values ('".mysqli_real_escape_string($this->connectionlink,$HarvestAssetScanID)."',NOW(),'1','".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['CustomPalletID'])."')";
					$q13 = mysqli_query($this->connectionlink,$query13);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}

					$query9 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` + 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['CustomPalletID'])."'";
					$q9 = mysqli_query($this->connectionlink,$query9);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					//End inserting into Custompallet

					//End create asset



					//start recording tracking
					
					$desc = '';
					$desc = $desc . "Component (SN : ".$data['repairMedia'][$i]['repair_out_sn'].", MPN : ".$data['repairMedia'][$i]['repair_out_mpn'].") removed from Serial (".$old['SerialNumber'].") into BIN (BIN ID : ".$data['repairMedia'][$i]['repair_bin_id'].").";
					$desc = $desc . " in Repair screen";
					//$desc = "Media (SN : ".$data['SanitizationMedia'][$i]['media_out_sn'].", MPN : ".$data['SanitizationMedia'][$i]['media_out_mpn'].") removed into BIN (Sanitization BIN ID : ".$data['SanitizationMedia'][$i]['BinName'].") and Media (SN : ".$data['SanitizationMedia'][$i]['media_in_sn'].", MPN : ".$data['SanitizationMedia'][$i]['media_in_mpn'].") added from Inventory in Sanitization screen";

					$query22 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$HarvestAssetScanID)."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
					$q22 = mysqli_query($this->connectionlink,$query22);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}

					//End recording tracking


					/*$query11 = "insert into asset_sanitization_media_details (AssetScanID,media_out_sn,media_out_mpn,media_bin_id,media_in_sn,media_in_mpn,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['media_out_sn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['media_out_mpn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['media_bin_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['media_in_sn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['media_in_mpn'])."',NOW(),'".$_SESSION['user']['UserId']."') ";*/					
					//$query11 = "insert into asset_repair_media_details (ParentAssetScanID,InventoryID,repair_out_sn,repair_out_mpn,repair_bin_id,repair_in_sn,repair_in_mpn,repair_bin_name,repair_id,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$old['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$inventory_id)."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_out_sn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_out_mpn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_bin_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_in_sn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_in_mpn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['BinName'])."',NOW(),'".$_SESSION['user']['UserId']."') ";					
				}				

				if( $data['repairMedia'][$i]['AssetScanID'] > 0) {
					//Start updating media out details
					//$query20 = "update inventory set UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',InventoryStatusID = '2',CustomPalletID = NULL,InventoryAddedToAssetScanID = '".mysqli_real_escape_string($this->connectionlink,$old['AssetScanID'])."',InventoryAddedToSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$old['SerialNumber'])."' where InventoryID = '".mysqli_real_escape_string($this->connectionlink,$data['SanitizationIn'][$i]['InventoryID'])."'";
					$query20 = "update asset set DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."',StatusID = '14',CustomPalletID = NULL,InventoryAddedToAssetScanID = '".mysqli_real_escape_string($this->connectionlink,$old['AssetScanID'])."',InventoryAddedToSerialNumber = '".mysqli_real_escape_string($this->connectionlink,$old['SerialNumber'])."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['AssetScanID'])."'";
					$q20 = mysqli_query($this->connectionlink,$query20);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					//$query21 = "delete from custompallet_items where InventoryID = '".mysqli_real_escape_string($this->connectionlink,$data['SanitizationIn'][$i]['InventoryID'])."'";
					$query21 = "delete from custompallet_items where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['AssetScanID'])."'";
					$q21 = mysqli_query($this->connectionlink,$query21);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}

					$query22 = "UPDATE `custompallet` SET `AssetsCount`= `AssetsCount` - 1 WHERE `CustomPalletID`='".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['media_in_CustomPalletID'])."'";
					$q22 = mysqli_query($this->connectionlink,$query22);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					//End updating media out details
				}


				if(($data['repairMedia'][$i]['repair_out_sn'] != '' && $data['repairMedia'][$i]['repair_out_sn'] != 'n/a') || $data['repairMedia'][$i]['AssetScanID'] > 0) {

					$query11 = "insert into asset_repair_media_details (ParentAssetScanID,parttypeid,COOID,input_id";

					if($HarvestAssetScanID > 0) { 
						$query11 = $query11 . ",AssetScanID";
					}				

					$query11 = $query11 . ",repair_out_sn,repair_out_mpn,ingested_serial_scan_time,ingested_mpn_scan_time";
					
					if($data['repairMedia'][$i]['CustomPalletID'] > 0) {
						$query11 = $query11 . ",repair_bin_id";
					}
					
					$query11 = $query11 . ",repair_in_sn,repair_in_mpn,repair_bin_name,repair_id,CreatedDate,CreatedBy";

					if($data['sub_disposition_id'] > 0) {
						$query11 = $query11 . ",repair_out_disposition_id";
					}
					

					$query11 = $query11 . ") values ('".mysqli_real_escape_string($this->connectionlink,$old['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['parttypeid'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['COOID'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['input_id'])."'";
					if($HarvestAssetScanID > 0) {
						$query11 = $query11. ",'".mysqli_real_escape_string($this->connectionlink,$HarvestAssetScanID)."'";
					}

					$query11 = $query11 . ",'".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_out_sn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_out_mpn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['ingested_serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['ingested_mpn_scan_time'])."'";
					
					if($data['repairMedia'][$i]['CustomPalletID'] > 0) {
						$query11 = $query11 . ",'".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['CustomPalletID'])."'";
					}

					$query11 = $query11 . ",'".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_in_sn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_in_mpn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['BinName'])."','".mysqli_real_escape_string($this->connectionlink,$id)."',NOW(),'".$_SESSION['user']['UserId']."'";

					if($data['sub_disposition_id'] > 0) {
						$query11 = $query11 . ",'".mysqli_real_escape_string($this->connectionlink,$data['sub_disposition_id'])."'";
					}
					$query11 = $query11 . ")";
					$q11 = mysqli_query($this->connectionlink,$query11);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink).$query11;
						return json_encode($json);
					}
					$media_id = mysqli_insert_id($this->connectionlink);
					
					$desc = '';
					if($data['repairMedia'][$i]['repair_out_sn'] != '' && $data['repairMedia'][$i]['repair_out_sn'] != 'n/a') {
						$desc = $desc . "Media (SN : ".$data['repairMedia'][$i]['repair_out_sn'].", MPN : ".$data['repairMedia'][$i]['repair_out_mpn'].") removed into BIN ( BIN ID : ".$data['repairMedia'][$i]['BinName'].").";
					}

					if($data['repairMedia'][$i]['AssetScanID'] > 0) {
						$desc = $desc . " Media (SN : ".$data['repairMedia'][$i]['repair_in_sn'].", MPN : ".$data['repairMedia'][$i]['repair_in_mpn'].") added from Inventory";
					}

					$desc = $desc . " in Repair screen";
					//$desc = "Media (SN : ".$data['repairMedia'][$i]['repair_out_sn'].", MPN : ".$data['repairMedia'][$i]['repair_out_mpn'].") removed into BIN ( BIN ID : ".$data['repairMedia'][$i]['BinName'].") and Media (SN : ".$data['repairMedia'][$i]['repair_in_sn'].", MPN : ".$data['repairMedia'][$i]['repair_in_mpn'].") added from Inventory in Repair screen";
					$query22 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$old['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
					$q22 = mysqli_query($this->connectionlink,$query22);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}


					if($data['repairMedia'][$i]['AssetScanID'] > 0) {

						$desc1 = "Serial added to another Serial (".$old['SerialNumber'].") and removed from BIN in Repair Screen";

						$query22 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc1)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
						$q22 = mysqli_query($this->connectionlink,$query22);
						if(mysqli_error($this->connectionlink)) {
							$json['Success'] = false;
							$json['Result'] = mysqli_error($this->connectionlink);
							return json_encode($json);
						}

					}				

					//End Tracking
					
				}
			}			



				/*$query11 = "insert into asset_repair_media_details (AssetScanID,repair_out_sn,repair_out_mpn,repair_bin_id,repair_in_sn,repair_in_mpn,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_out_sn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_out_mpn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_bin_id'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_in_sn'])."','".mysqli_real_escape_string($this->connectionlink,$data['repairMedia'][$i]['repair_in_mpn'])."',NOW(),'".$_SESSION['user']['UserId']."') ";
				$q11 = mysqli_query($this->connectionlink,$query11);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
			}*/
			//End insert media details

			//Start Close Container
			if($data['CloseBin'] == '1') {


				$close_bin = $this->EmptyCustomPallet($data['FromCustomPalletID'],$data['AllDispositionBINName']);
				if($close_bin['Success']) {
					$json['BinClosed'] = '1';
					$json['CloseMessage'] = $close_bin['Error'];
					//Start Admin Tracking				
					$query13 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."','BIN Emptied in Repair Screen',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','','','','')";
					$q13 = mysqli_query($this->connectionlink,$query13);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					//End Admin Trackin

				} else {
					$json['CloseMessage'] = $close_bin['Error'];
				}


				// $query11 = "select count(*) from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."'";
				// $q11 = mysqli_query($this->connectionlink,$query11);
				// if(mysqli_error($this->connectionlink)) {
				// 	$json['Success'] = false;
				// 	$json['Result'] = mysqli_error($this->connectionlink);
				// 	return json_encode($json);
				// }
				// if(mysqli_affected_rows($this->connectionlink) > 0) {
				// 	$row11 = mysqli_fetch_assoc($q11);
				// 	if($row11['count(*)'] == 0) {//Custom Pallet can be closed
				// 		$query12 = "update custompallet set StatusID = '3',LastModifiedDate = NOW(),LastModifiedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."'";
				// 		$q12 = mysqli_query($this->connectionlink,$query12);
				// 		if(mysqli_error($this->connectionlink)) {
				// 			$json['Success'] = false;
				// 			$json['Result'] = mysqli_error($this->connectionlink);
				// 			return json_encode($json);
				// 		}

				// 		//Start Admin Tracking				
				// 		$query13 = "insert into admin_tracking (ItemType,ItemName,Item,Action,CreatedDate,CreatedBy,AccountID,UniqueID,`Table`,`ReferenceID`,`RequestName`) values ('BIN','Transaction','".mysqli_real_escape_string($this->connectionlink,$data['FromCustomPalletID'])."','BIN closed in repair Screen',NOW(),'".$_SESSION['user']['UserId']."','".$_SESSION['user']['AccountID']."','','','','')";
				// 		$q13 = mysqli_query($this->connectionlink,$query13);
				// 		if(mysqli_error($this->connectionlink)) {
				// 			$json['Success'] = false;
				// 			$json['Result'] = mysqli_error($this->connectionlink);
				// 			return json_encode($json);
				// 		}
				// 		//End Admin Tracking

				// 	} else { //Items still exists, can't close
				// 		$json['CloseMessage'] = $row11['count(*)'].' Serials exists in the BIN, Not able to close BIN';
				// 	}
				// }
			}
			//End close BIN

			//Start get Asset Details			
			if($id > 0) {
				$query10 = "select a.*,d.disposition,i.input,i.input_type,cp.CustomPalletID,cp.BinName,cp.AssetsCount,pt.parttype,co.COO from asset_repair a 
				left join custompallet cp on a.ToCustomPalletID = cp.CustomPalletID 
				left join disposition d on a.disposition_id = d.disposition_id  
				left join workflow_input i on a.repair_input_id = i.input_id 
				left join parttype pt on a.parttypeid = pt.parttypeid 
				left join COO co on a.COOID = co.COOID 
				where a.repair_id = '".mysqli_real_escape_string($this->connectionlink,$id)."'";
				$q10 = mysqli_query($this->connectionlink,$query10);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row10 = mysqli_fetch_assoc($q10);
					$json['Asset'] = $row10;
				}
			}			
			//End get Asset Details


			$json['Success'] = true;
			$json['Result'] = 'Success';
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}


	public function GetrepairAssets ($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);	
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'repair')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to repair Page';
				return json_encode($json);
			}
			// $query = "select a.*,d.disposition,i.input,i.input_type from asset_repair a 
			// left join disposition d on a.disposition_id = d.disposition_id  
			// left join workflow_input i on a.repair_input_id = i.input_id 
			// where a.FromCustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";


			$query = "select a.*,d.disposition,i.input,i.input_type,pt.parttype,co.COO from asset_repair a 
			left join disposition d on a.disposition_id = d.disposition_id  
			left join workflow_input i on a.repair_input_id = i.input_id 
			left join parttype pt on a.parttypeid = pt.parttypeid 
			left join COO co on a.COOID = co.COOID   
			where a.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and a.CreatedBy = '".$_SESSION['user']['UserId']."' and  DATE(CreatedDate) = CURDATE()";

			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {

						if($key == 'FromBinName') {
							$query = $query . " AND a.FromBinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'SerialNumber') {
							$query = $query . " AND a.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'UniversalModelNumber') {
							$query = $query . " AND a.UniversalModelNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'input') {
							$query = $query . " AND i.input like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'repair_notes') {
							$query = $query . " AND a.repair_notes like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'disposition') {
							$query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'ToBinName') {
							$query = $query . " AND a.ToBinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}

						if($key == 'parttype') {
							$query = $query . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'COO') {
							$query = $query . " AND co.COO like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
									
					}
				}
			}
			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}
	
				  if($data['OrderBy'] == 'FromBinName') {
					$query = $query . " order by a.FromBinName ".$order_by_type." ";
				}
				else if($data['OrderBy'] == 'SerialNumber') {
					$query = $query . " order by a.SerialNumber ".$order_by_type." ";
				}  
				else if($data['OrderBy'] == 'UniversalModelNumber') {
					$query = $query . " order by a.UniversalModelNumber ".$order_by_type." ";
				} 
				else if($data['OrderBy'] == 'input') {
					$query = $query . " order by i.input ".$order_by_type." ";
				}
				elseif($data['OrderBy'] == 'repair_notes') {
					$query = $query . " order by a.repair_notes ".$order_by_type." ";
				} 
				else if($data['OrderBy'] == 'disposition') {
					$query = $query . " order by d.disposition ".$order_by_type." ";
				}
				else if($data['OrderBy'] == 'ToBinName') {
					$query = $query . " order by a.ToBinName ".$order_by_type." ";
				}

				else if($data['OrderBy'] == 'parttype') {
					$query = $query . " order by pt.parttype ".$order_by_type." ";
				}
				else if($data['OrderBy'] == 'COO') {
					$query = $query . " order by co.COO ".$order_by_type." ";
				}
				
			} else {
				$query = $query . " order by a.CreatedDate desc ";
			}			

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));

			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
			if(mysqli_affected_rows($this->connectionlink) > 0) {
				$i = 0;
				//$assets = array();
				while($row = mysqli_fetch_assoc($q)) {

					//Start get Recovered Inventory
					$row['MediaOut'] = array();
					$j = 0;
					//$query1 = "select SerialNumber from inventory where event_id = '".mysqli_real_escape_string($this->connectionlink,$row['event_id'])."'";
					$query1 = "select SerialNumber from asset where event_id = '".mysqli_real_escape_string($this->connectionlink,$row['event_id'])."'";
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						while($row1 = mysqli_fetch_assoc($q1)) {
							$row['MediaOut'][$j] = $row1;
							$j++;
						}
					}
					//End get Recovered Inventory


					//Start get Ingested Inventory
					$row['MediaIn'] = array();
					$j = 0;
					//$query1 = "select SerialNumber from inventory where InventoryAddedToAssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."'";
					$query1 = "select SerialNumber from asset where InventoryAddedToAssetScanID = '".mysqli_real_escape_string($this->connectionlink,$row['AssetScanID'])."'";
					$q1 = mysqli_query($this->connectionlink,$query1);
					if(mysqli_error($this->connectionlink)) {
						$json['Success'] = false;
						$json['Result'] = mysqli_error($this->connectionlink);
						return json_encode($json);
					}
					if(mysqli_affected_rows($this->connectionlink) > 0) {
						while($row1 = mysqli_fetch_assoc($q1)) {
							$row['MediaIn'][$j] = $row1;
							$j++;
						}
					}
					//End get Ingested Inventory

					$assets[$i] = $row;
					$i++;
				}
				$json['Success'] = true;
				$json['Result'] = $assets;
				//return json_encode($json);
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No Repair Assets Available';
				//return json_encode($json);
			}
		      if($data['skip'] == 0) {
				// $query1 = "select count(*) from asset_repair a 
				// left join disposition d on a.disposition_id = d.disposition_id  
				// left join workflow_input i on a.repair_input_id = i.input_id 
				// where a.FromCustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";

				$query1 = "select count(*) from asset_repair a 
				left join disposition d on a.disposition_id = d.disposition_id  
				left join workflow_input i on a.repair_input_id = i.input_id 
				left join parttype pt on a.parttypeid = pt.parttypeid 
				left join COO co on a.COOID = co.COOID 
				where a.SiteID = '".mysqli_real_escape_string($this->connectionlink,$data['SiteID'])."' and a.CreatedBy = '".$_SESSION['user']['UserId']."' and  DATE(CreatedDate) = CURDATE()";

				if($data[0] && count($data[0]) > 0) {
					foreach ($data[0] as $key => $value) {
						if($value != '') {
							
							if($key == 'FromBinName') {
								$query1 = $query1 . " AND a.FromBinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'SerialNumber') {
								$query1 = $query1 . " AND a.SerialNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'UniversalModelNumber') {
								$query1 = $query1 . " AND a.UniversalModelNumber like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'input') {
								$query1 = $query1 . " AND i.input like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'repair_notes') {
								$query1 = $query1 . " AND a.repair_notes like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'disposition') {
								$query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'ToBinName') {
								$query1 = $query1 . " AND a.ToBinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}

							if($key == 'parttype') {
								$query1 = $query1 . " AND pt.parttype like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
							if($key == 'COO') {
								$query1 = $query1 . " AND co.COO like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
							}
						}
					}
				}

				$q1 = mysqli_query($this->connectionlink,$query1);
				if(mysqli_error($this->connectionlink)) {
					$json['Success'] = false;
					$json['Result'] = mysqli_error($this->connectionlink);
					return json_encode($json);
				}
				if(mysqli_affected_rows($this->connectionlink) > 0) {
					$row1 = mysqli_fetch_assoc($q1);
					$count = $row1['count(*)'];
				}
				$json['total'] = $count;
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	// Actions Functions for Bin Management

	public function CreateBin($data) {
		if (!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}

		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Repair')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Repair Page';
				return json_encode($json);
			}

			// Validate required fields
			if (empty($data['BinName']) || empty($data['idPackage']) || empty($data['FacilityID'])) {
				$json['Success'] = false;
				$json['Result'] = 'Bin Name, Bin Type, and Facility are required';
				return json_encode($json);
			}

			// Validate bin name prefix based on container type requirements
			$binName = mysqli_real_escape_string($this->connectionlink, $data['BinName']);
			$packageId = mysqli_real_escape_string($this->connectionlink, $data['idPackage']);

			// Get container type details including PrefixRequiredForBinName
			$packageQuery = "SELECT packageName, PrefixRequiredForBinName FROM package WHERE idPackage = '$packageId'";
			$packageResult = mysqli_query($this->connectionlink, $packageQuery);

			if (mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = 'Error validating container type: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if (mysqli_num_rows($packageResult) > 0) {
				$packageData = mysqli_fetch_assoc($packageResult);

				// Check if prefix is required for this container type
				if ($packageData['PrefixRequiredForBinName'] == '1') {
					$requiredPrefix = strtoupper(substr($packageData['packageName'], 0, 3));
					$binNamePrefix = strtoupper(substr($binName, 0, 3));

					if ($binNamePrefix !== $requiredPrefix) {
						$json['Success'] = false;
						$json['Result'] = 'Bin name must start with "' . $requiredPrefix . '" (first 3 characters of container type "' . $packageData['packageName'] . '")';
						return json_encode($json);
					}
				}
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Invalid container type selected';
				return json_encode($json);
			}

			$userId = $_SESSION['user']['UserId'];
			$binName = mysqli_real_escape_string($this->connectionlink, $data['BinName']);
			$idPackage = mysqli_real_escape_string($this->connectionlink, $data['idPackage']);
			$facilityID = mysqli_real_escape_string($this->connectionlink, $data['FacilityID']);
			$locationType = isset($data['LocationType']) ? mysqli_real_escape_string($this->connectionlink, $data['LocationType']) : 'WIP';
			$description = isset($data['Notes']) ? mysqli_real_escape_string($this->connectionlink, $data['Notes']) : '';
			$statusID = isset($data['StatusID']) ? mysqli_real_escape_string($this->connectionlink, $data['StatusID']) : '1';
			$acceptAllDisposition = isset($data['AcceptAllDisposition']) ? mysqli_real_escape_string($this->connectionlink, $data['AcceptAllDisposition']) : '0';
			$maxLimitRequired = isset($data['MaxLimitRequired']) ? mysqli_real_escape_string($this->connectionlink, $data['MaxLimitRequired']) : '0';
			$customerLock = isset($data['CustomerLock']) ? mysqli_real_escape_string($this->connectionlink, $data['CustomerLock']) : '0';
			$referenceIDRequired = isset($data['ReferenceIDRequired']) ? mysqli_real_escape_string($this->connectionlink, $data['ReferenceIDRequired']) : '0';
			$dispositionID = isset($data['disposition_id']) ? mysqli_real_escape_string($this->connectionlink, $data['disposition_id']) : 'NULL';
			$locationID = isset($data['LocationID']) && !empty($data['LocationID']) ? mysqli_real_escape_string($this->connectionlink, $data['LocationID']) : 'NULL';
			$groupID = isset($data['LocationGroupID']) && !empty($data['LocationGroupID']) ? mysqli_real_escape_string($this->connectionlink, $data['LocationGroupID']) : 'NULL';

			// Check if bin name already exists
			$checkQuery = "SELECT COUNT(*) as count FROM custompallet WHERE BinName = '$binName'";
			$checkResult = mysqli_query($this->connectionlink, $checkQuery);
			$checkRow = mysqli_fetch_assoc($checkResult);

			if ($checkRow['count'] > 0) {
				$json['Success'] = false;
				$json['Result'] = 'Bin name already exists. Please choose a different name.';
				return json_encode($json);
			}

			// If GroupID is provided, find and assign an available location (same as admin module)
			if (!empty($groupID) && $groupID != 'NULL') {
				// Check if there are empty locations in the group (same query as admin module)
				$checkQuery = "SELECT count(*) FROM location WHERE GroupID = '$groupID' AND Locked = '2' AND LocationStatus = '1'";
				$checkResult = mysqli_query($this->connectionlink, $checkQuery);

				if (!$checkResult) {
					$json['Success'] = false;
					$json['Result'] = 'Database error while checking locations: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				if (mysqli_num_rows($checkResult) > 0) {
					$checkRow = mysqli_fetch_assoc($checkResult);
					if ($checkRow['count(*)'] < 1) {
						$json['Success'] = false;
						$json['Result'] = 'No empty locations available in selected Location Group';
						return json_encode($json);
					}
				} else {
					$json['Success'] = false;
					$json['Result'] = 'Invalid Location Group';
					return json_encode($json);
				}

				// Get the first available location (same pattern as admin module)
				$locationQuery = "SELECT LocationID, LocationName
								  FROM location
								  WHERE GroupID = '$groupID'
								  AND Locked = '2'
								  AND LocationStatus = '1'
								  ORDER BY LocationName
								  LIMIT 1";
				$locationResult = mysqli_query($this->connectionlink, $locationQuery);

				if (!$locationResult) {
					$json['Success'] = false;
					$json['Result'] = 'Database error while searching for locations: ' . mysqli_error($this->connectionlink);
					return json_encode($json);
				}

				if (mysqli_num_rows($locationResult) > 0) {
					$locationRow = mysqli_fetch_assoc($locationResult);
					$locationID = $locationRow['LocationID'];

					// Lock the location (same as admin module pattern)
					$lockQuery = "UPDATE location
								  SET Locked = '1',
									  currentItemType = 'Bin',
									  currentItemID = '$binName'
								  WHERE LocationID = '$locationID'";
					$lockResult = mysqli_query($this->connectionlink, $lockQuery);

					if (!$lockResult) {
						$json['Success'] = false;
						$json['Result'] = 'Failed to lock location: ' . mysqli_error($this->connectionlink);
						return json_encode($json);
					}
				} else {
					// No available location found - return error
					$json['Success'] = false;
					$json['Result'] = 'No empty locations available in selected Location Group';
					return json_encode($json);
				}
			} else {
				// No GroupID provided, set LocationID to NULL
				$locationID = 'NULL';
			}

			// Insert new bin
			$insertQuery = "INSERT INTO custompallet (
				BinName,
				idPackage,
				BinType,
				FacilityID,
				LocationType,
				LocationID,
				GroupID,
				Description,
				StatusID,
				AcceptAllDisposition,
				MaxLimitRequired,
				CustomerLock,
				ReferenceIDRequired,
				disposition_id,
				AssetsCount,
				CreatedDate,
				CreatedBy
			) VALUES (
				'$binName',
				'$idPackage',
				'Physical',
				'$facilityID',
				'$locationType',
				$locationID,
				$groupID,
				'$description',
				'$statusID',
				'$acceptAllDisposition',
				'$maxLimitRequired',
				'$customerLock',
				'$referenceIDRequired',
				$dispositionID,
				0,
				NOW(),
				'$userId'
			)";

			$insertResult = mysqli_query($this->connectionlink, $insertQuery);

			if (!$insertResult) {
				$json['Success'] = false;
				$json['Result'] = 'Failed to create bin: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$customPalletID = mysqli_insert_id($this->connectionlink);

			// Add tracking record with disposition and location group information
			$trackingAction = "Bin was Created from Repair module";

			// Get disposition name if disposition_id is provided
			$dispositionName = '';
			if ($dispositionID != 'NULL') {
				$dispQuery = "SELECT disposition FROM disposition WHERE disposition_id = $dispositionID";
				$dispResult = mysqli_query($this->connectionlink, $dispQuery);
				if ($dispResult && mysqli_num_rows($dispResult) > 0) {
					$dispRow = mysqli_fetch_assoc($dispResult);
					$dispositionName = $dispRow['disposition'];
				}
			}

			// Get location group name if groupID is provided
			$locationGroupName = '';
			if ($groupID != 'NULL') {
				$groupQuery = "SELECT GroupName FROM location_group WHERE GroupID = $groupID";
				$groupResult = mysqli_query($this->connectionlink, $groupQuery);
				if ($groupResult && mysqli_num_rows($groupResult) > 0) {
					$groupRow = mysqli_fetch_assoc($groupResult);
					$locationGroupName = $groupRow['GroupName'];
				}
			}

			// Build detailed tracking action
			$trackingDetails = array();
			if (!empty($dispositionName)) {
				$trackingDetails[] = "Disposition: $dispositionName";
			}
			if (!empty($locationGroupName)) {
				$trackingDetails[] = "Location Group: $locationGroupName";
			}

			if (!empty($trackingDetails)) {
				$trackingAction .= " (" . implode(", ", $trackingDetails) . ")";
			}

			$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
							  VALUES ('$customPalletID', '$binName', '$trackingAction', NOW(), '$userId', 'Repair')";
			mysqli_query($this->connectionlink, $trackingQuery);

			$json['Success'] = true;
			$json['Result'] = 'Bin created successfully with ID: ' . $customPalletID;

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = 'Error: ' . $e->getMessage();
		}

		return json_encode($json);
	}

	public function CloseBin($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Repair')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Repair Page';
				return json_encode($json);
			}

			// Validate TPVR fields
			if(empty($data['AuditController']) || empty($data['Password']) || empty($data['NewSealID']) || empty($data['BinWeight'])) {
				$json['Success'] = false;
				$json['Result'] = 'All TPVR fields are required (Controller, Password, Seal ID, Weight).';
				return json_encode($json);
			}

			// Get bin name from database first
			$binNameQuery = "SELECT BinName FROM custompallet WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$binNameResult = mysqli_query($this->connectionlink, $binNameQuery);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if(mysqli_num_rows($binNameResult) == 0) {
				$json['Success'] = false;
				$json['Result'] = 'Bin not found';
				return json_encode($json);
			}

			$binNameRow = mysqli_fetch_assoc($binNameResult);
			$binName = $binNameRow['BinName'];

			// Update bin status to closed with TPVR information (matching failure_analysis pattern)
			$query = "UPDATE custompallet SET StatusID = '3', SealID = '".mysqli_real_escape_string($this->connectionlink,$data['NewSealID'])."',
					  ContainerWeight = '".mysqli_real_escape_string($this->connectionlink,$data['BinWeight'])."',
					  LastModifiedDate = NOW(), LastModifiedBy = '".$_SESSION['user']['UserId']."'
					  WHERE CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."'";
			$q = mysqli_query($this->connectionlink,$query);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Add tracking record with TPVR details (matching failure_analysis pattern)
			$trackingAction = "Bin closed in Repair module. Controller: " . $data['AuditController'] .
							  ", Seal ID: " . $data['NewSealID'] . ", Weight: " . $data['BinWeight'];

			$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
							  VALUES ('".mysqli_real_escape_string($this->connectionlink,$data['CustomPalletID'])."',
									  '".mysqli_real_escape_string($this->connectionlink,$binName)."',
									  '".mysqli_real_escape_string($this->connectionlink,$trackingAction)."', NOW(), '".$_SESSION['user']['UserId']."', 'Repair')";
			mysqli_query($this->connectionlink, $trackingQuery);

			$json['Success'] = true;
			$json['Result'] = 'Bin closed successfully: ' . $binName;
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function NestToBin($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Repair')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Repair Page';
				return json_encode($json);
			}

			// Validate required fields
			if(empty($data['CustomPalletID']) || empty($data['parentBin'])) {
				$json['Success'] = false;
				$json['Result'] = 'Bin ID and Parent Bin are required';
				return json_encode($json);
			}

			$customPalletID = mysqli_real_escape_string($this->connectionlink, $data['CustomPalletID']);
			$parentBinName = mysqli_real_escape_string($this->connectionlink, $data['parentBin']);
			$userId = $_SESSION['user']['UserId'];

			// Get current bin details
			$binQuery = "SELECT BinName, StatusID, ParentCustomPalletID FROM custompallet WHERE CustomPalletID = '$customPalletID'";
			$binResult = mysqli_query($this->connectionlink, $binQuery);

			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if(mysqli_num_rows($binResult) == 0) {
				$json['Success'] = false;
				$json['Result'] = 'Invalid bin ID';
				return json_encode($json);
			}

			$binData = mysqli_fetch_assoc($binResult);
			$binName = $binData['BinName'];

			// Check if bin is already nested
			if(!empty($binData['ParentCustomPalletID'])) {
				$json['Success'] = false;
				$json['Result'] = 'Bin is already nested under another bin';
				return json_encode($json);
			}

			// Validate parent bin
			$parentQuery = "SELECT CustomPalletID, StatusID, disposition_id FROM custompallet WHERE BinName = '$parentBinName'";
			$parentResult = mysqli_query($this->connectionlink, $parentQuery);

			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if(mysqli_num_rows($parentResult) == 0) {
				$json['Success'] = false;
				$json['Result'] = 'Parent bin not found';
				return json_encode($json);
			}

			$parentData = mysqli_fetch_assoc($parentResult);
			$parentCustomPalletID = $parentData['CustomPalletID'];

			// Check if trying to nest to itself
			if($customPalletID == $parentCustomPalletID) {
				$json['Success'] = false;
				$json['Result'] = 'Cannot nest bin to itself';
				return json_encode($json);
			}

			// Update bin to nest under parent
			$updateQuery = "UPDATE custompallet
							SET ParentCustomPalletID = '$parentCustomPalletID',
								disposition_id = '{$parentData['disposition_id']}'
							WHERE CustomPalletID = '$customPalletID'";

			$updateResult = mysqli_query($this->connectionlink, $updateQuery);

			if(!$updateResult) {
				$json['Success'] = false;
				$json['Result'] = 'Failed to nest bin: ' . mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			// Remove from station mapping since it's now nested
			$removeStationQuery = "DELETE FROM station_custompallet_mapping WHERE CustomPalletID = '$customPalletID'";
			mysqli_query($this->connectionlink, $removeStationQuery);

			// Add tracking record
			$trackingAction = "Bin nested under parent bin '$parentBinName' from Repair module";
			$trackingQuery = "INSERT INTO custompallet_tracking (CustomPalletID, BinName, Action, CreatedDate, CreatedBy, ModuleName)
							  VALUES ('$customPalletID', '$binName', '$trackingAction', NOW(), '$userId', 'Repair')";
			mysqli_query($this->connectionlink, $trackingQuery);

			$json['Success'] = true;
			$json['Result'] = "Bin '$binName' successfully nested under '$parentBinName'";
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetSessionFacility($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			$facilityID = $_SESSION['user']['FacilityID'];
			$query = "SELECT FacilityID, FacilityName FROM facility WHERE FacilityID = '$facilityID'";
			$result = mysqli_query($this->connectionlink, $query);

			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if(mysqli_num_rows($result) > 0) {
				$row = mysqli_fetch_assoc($result);
				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Facility not found';
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetBinPackageTypes($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			$facilityID = isset($data['FacilityID']) ? mysqli_real_escape_string($this->connectionlink, $data['FacilityID']) : $_SESSION['user']['FacilityID'];

			$query = "SELECT idPackage, packageName FROM package WHERE Active = '1' AND FacilityID = '$facilityID' ORDER BY packageName";
			$result = mysqli_query($this->connectionlink, $query);

			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$packages = array();
			if(mysqli_num_rows($result) > 0) {
				while($row = mysqli_fetch_assoc($result)) {
					$packages[] = $row;
				}
				$json['Success'] = true;
				$json['Result'] = $packages;
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No package types found';
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetStationLocationGroup($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			if(empty($data['SiteID'])) {
				$json['Success'] = false;
				$json['Result'] = 'Site ID is required';
				return json_encode($json);
			}

			$siteID = mysqli_real_escape_string($this->connectionlink, $data['SiteID']);

			$query = "SELECT lg.GroupID as LocationGroupID, lg.GroupName
					  FROM site s
					  LEFT JOIN location_group lg ON s.GroupID = lg.GroupID
					  WHERE s.SiteID = '$siteID'";
			$result = mysqli_query($this->connectionlink, $query);

			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			if(mysqli_num_rows($result) > 0) {
				$row = mysqli_fetch_assoc($result);
				$json['Success'] = true;
				$json['Result'] = $row;
			} else {
				$json['Success'] = false;
				$json['Result'] = 'Station location information not found';
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

	public function GetAvailableParentBins($data) {
		if(!isset($_SESSION['user'])) {
			$json['Success'] = false;
			$json['Result'] = 'Login to continue';
			return json_encode($json);
		}
		$json = array(
			'Success' => false,
			'Result' => 'No Data'
		);
		try {
			$facilityID = $_SESSION['user']['FacilityID'];

			$query = "SELECT CustomPalletID, BinName FROM custompallet
					  WHERE StatusID = '1'
					  AND FacilityID = '$facilityID'
					  AND ParentCustomPalletID IS NULL
					  ORDER BY BinName";
			$result = mysqli_query($this->connectionlink, $query);

			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}

			$bins = array();
			if(mysqli_num_rows($result) > 0) {
				while($row = mysqli_fetch_assoc($result)) {
					$bins[] = $row;
				}
				$json['Success'] = true;
				$json['Result'] = $bins;
			} else {
				$json['Success'] = false;
				$json['Result'] = 'No available parent bins found';
			}
			return json_encode($json);

		} catch (Exception $e) {
			$json['Success'] = false;
			$json['Result'] = $e->getMessage();
			return json_encode($json);
		}
	}

}
?>