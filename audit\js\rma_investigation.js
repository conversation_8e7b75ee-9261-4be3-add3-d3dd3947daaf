(function () {
    'use strict';

    angular.module('app').controller("rma_investigation", function ($scope,$http,$filter,$upload,$rootScope,$mdToast,$mdDialog,$stateParams,$window) {

        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'administration/includes/admin_extended_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=CheckIfPagePermission&Page=RMA Investigation',
            success: function (data) {
                $rootScope.$broadcast('preloader:hide');
                if (data.Success) {                
                } else {
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-info md-block')
                    );  
                    window.location = host;             
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $rootScope.$broadcast('preloader:hide');
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.GetCurrentTime = function(object,item) {
            if (!object || typeof object !== 'object') {
              console.log('Invalid scope object provided');
            }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }
                    console.log('Scan Object = '+JSON.stringify(object));
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };


        $scope.PartTypes = [];
        $scope.COOList = [];


        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetPartTypesAndCOO',
            success: function(data){
                if(data.Success) {
                    if(data.PartTypes) {
                        $scope.PartTypes = data.PartTypes;
                    }
                    if(data.COOList) {
                        $scope.COOList = data.COOList;
                    }
                } else {
                    $scope.PartTypes = [];
                    $scope.COOList = [];
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.GetCurrentTime = function(object,item) {
            if (!object || typeof object !== 'object') {
              console.log('Invalid scope object provided');
            }
            jQuery.ajax({
                url: host+'recovery/includes/recovery_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetCurrentTime',
                success: function(data){
                    if(data.Success) {
                        object[item] = data.Result;
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Invalid')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        object[item] = '';
                    }
                    //console.log('Scan Object = '+JSON.stringify(object));
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.asset = {'rma_custom_id' : 'n/a','rma_notes' : 'n/a'};
        $scope.CustomPalletID = '';
        $scope.InputResults = [];
        $scope.Stations = [];
        $scope.StationCustomPallets = [];
        $scope.DefaultInputID = '';
        $scope.Assets = [];
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetInputResults&Workflow=RMA Investigation&workflow_id=4',
            success: function(data){
                if(data.Success) {
                    $scope.InputResults = data.Result;
                    if(data.Default) {
                        $scope.asset.rma_input_id = data.Default;
                        $scope.DefaultInputID = data.Default;
                    }
                } else {
                    $scope.InputResults = [];
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                initSessionTime(); $scope.$apply();
            }
        });


        jQuery.ajax({
            url: host+'audit/includes/audit_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetFacilityStations&Workflow=RMA Investigation&workflow_id=4',
            success: function(data){
                if(data.Success) {
                    $scope.Stations = data.Result;
                } else {
                    $scope.Stations = [];
                    $mdToast.show(
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });

        // Initialize variables for bin management
        $scope.SessionFacility = {};
        $scope.Facility = [];
        $scope.PackageTypes = [];
        $scope.StationLocationInfo = {};

        // Get facility data on page load for bin creation
        $scope.getFacilityDataOnLoad = function() {
            jQuery.ajax({
                url: host + 'audit/includes/rma_investigation_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetSessionFacility',
                success: function(data) {
                    if(data.Success) {
                        $scope.SessionFacility = data.Result;
                        $scope.Facility = [data.Result]; // Make it an array for the dropdown
                    }
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(data) {
                    initSessionTime();
                    $scope.$apply();
                }
            });

            // Get package types for bin creation - will be loaded when needed in CreateBin function
        };

        // Get station location info for bin creation
        $scope.getStationLocationInfo = function(siteID) {
            jQuery.ajax({
                url: host + 'audit/includes/rma_investigation_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetStationLocationGroup&SiteID=' + siteID,
                success: function(data) {
                    if(data.Success) {
                        $scope.StationLocationInfo = data.Result;
                        // Populate location group in create bin form
                        if($scope.createBinData) {
                            $scope.createBinData.LocationGroup = data.Result.GroupName || '';
                            $scope.createBinData.LocationID = data.Result.LocationID || '';
                            $scope.createBinData.LocationGroupID = data.Result.LocationGroupID || '';
                        }
                    }
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(data) {
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        // Get facility data on page load for bin creation
        $scope.getFacilityDataOnLoad();

        // Get bin package types for create bin
        $scope.GetBinPackageTypesForCreateBin = function() {
            if ($scope.SessionFacility && $scope.SessionFacility.FacilityID) {
                jQuery.ajax({
                    url: host + 'audit/includes/rma_investigation_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetBinPackageTypes&FacilityID=' + $scope.SessionFacility.FacilityID,
                    success: function (data) {
                        if (data.Success) {
                            $scope.PackageTypes = data.Result;
                        }
                        $scope.$apply();
                    }
                });
            }
        };

        $scope.GetStationCustomPallets = function () {
            if($scope.SiteID) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetStationCustomPallets&SiteID='+$scope.SiteID+'&Workflow=RMA Investigation&workflow_id=4&CustomPalletID='+$scope.CustomPalletID,
                    success: function(data){
                        if(data.Success) {
                            $scope.StationCustomPallets = data.Result;
                        } else {
                            $scope.StationCustomPallets = [];
                            $scope.SiteID = '';
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.StationCustomPallets = [];
            }
        };

        $scope.MapCustomPalletToDisposition = function (item) {
            if($scope.SiteID) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MapCustomPalletToDisposition&SiteID='+$scope.SiteID+'&Workflow=RMA Investigation&workflow_id=4'+'&'+$.param(item)+'&SourceCustomPalletID='+$scope.CustomPalletID,
                    success: function(data) {
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            item.CustomPalletID = data.CustomPalletID;
                            item.AssetsCount = data.AssetsCount;
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            item.BinName = '';
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };

        $scope.GetCustomPalletDetails = function () {
            if($scope.BinName) {

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/rma_investigation_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetCustomPalletDetails&BinName='+$scope.BinName+'&SiteID='+$scope.SiteID,
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $scope.CustomPalletID = data.CustomPalletID;
                           // $scope.GetRMAAssets();
                            $scope.CallServerFunction(0);
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            $scope.CustomPalletID = '';
                            $scope.BinName = '';
                           // $scope.GetRMAAssets();
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {
                $scope.CustomPalletID = '';
            }
        };

      /*  $scope.GetRMAAssets = function () {
            if($scope.CustomPalletID > 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/rma_investigation_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetRMAAssets&CustomPalletID='+$scope.CustomPalletID,
                    success: function(data) {
                        if(data.Success) {
                            $scope.Assets = data.Result;
                        } else {
                            $scope.Assets = [];
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.Assets = [];
            }
        };
*/


        $scope.busy = false;
        $scope.AssetsSanizedPanel = [];
        $scope.Assets = [];
       // $scope.pagedItems = [];

        //Start Pagination Logic
        $scope.itemsPerPage = 20;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function() {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function() {
            $scope.currentPage =  $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage);
        };
        $scope.setPage = function(n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            //if($scope.CustomPalletID > 0)  {
            if($scope.SiteID > 0)  {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/rma_investigation_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetRMAAssets&limit='+$scope.itemsPerPage+'&CustomPalletID='+$scope.CustomPalletID+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text))+'&SiteID='+$scope.SiteID,
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {

                            $scope.Assets = data.Result;
                            if(data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            // $mdToast.show (
                            //     $mdToast.simple()
                            //         .content(data.Result)
                            //         .action('OK')
                            //         .position('right')
                            //         .hideDelay(0)
                            //         .toastClass('md-toast-danger md-block')
                            // );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.$watch("currentPage", function(newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            $scope.OrderBy = orderby;
            if($scope.OrderByType == 'asc') {
                $scope.OrderByType = 'desc';
            } else {
                $scope.OrderByType = 'asc';
            }
            $scope.busy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host+'audit/includes/rma_investigation_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetRMAAssets&limit='+$scope.itemsPerPage+'&CustomPalletID='+$scope.CustomPalletID+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.Assets = data.Result;
                        if(data.total) {
                            $scope.total = data.total;
                        }
                    } else {
                        // $mdToast.show (
                        //     $mdToast.simple()
                        //         .content(data.Result)
                        //         .action('OK')
                        //         .position('right')
                        //         .hideDelay(0)
                        //         .toastClass('md-toast-danger md-block')
                        // );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $scope.busy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.MakeFilter = function () {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        //End Pagination Logic

        $scope.disposition_color = '';
        $scope.sub_disposition_color = '';
        $scope.ApplyBusinessRule = function () {
            //if($scope.asset.rma_input_id > 0) {
            if($scope.asset.rma_input_id > 0 && $scope.asset.parttypeid > 0 && $scope.asset.COOID > 0) {            
                $scope.asset.input_id = $scope.asset.rma_input_id;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ApplyBusinessRule&workflow_id=4&'+$.param($scope.asset)+'&SiteID='+$scope.SiteID,
                    success: function(data){
                        if(data.Success) {
                            if (data.ExactMPN) {
                                $scope.asset.UniversalModelNumber = data.ExactMPN;
                            }
                            $scope.asset.disposition_id = data.Result.disposition_id;
                            $scope.asset.disposition = data.Result.disposition;
                            $scope.asset.rma_rule_id = data.Result.rule_id;
                            if(data.CustomPalletID) {
                                $scope.asset.CustomPalletID = data.CustomPalletID;
                                $scope.asset.BinName = data.BinName;
                                $scope.GetCurrentTime($scope.asset,'bin_scan_time');
                            } else {
                                $scope.asset.CustomPalletID = '';
                                $scope.asset.BinName = '';
                            }
                            $scope.asset.rule_description = data.Result.rule_description;
                            $scope.asset.rule_id_text  = data.Result.rule_id_text;
                            $scope.asset.ManufacturerSerialNumber = $scope.asset.SerialNumber;
                            $scope.disposition_color = data.Result.color_code;
                            $scope.sub_disposition_color = data.Result.sub_color_code;
                            $window.document.getElementById('scan_for_save').focus();
                        } else {
                            if (data.ExactMPN) {
                                $scope.asset.UniversalModelNumber = data.ExactMPN;
                            }
                            $scope.asset.disposition_id = '';
                            $scope.asset.disposition = '';
                            $scope.disposition_color = '';
                            $scope.asset.CustomPalletID = '';
                            $scope.asset.BinName = '';
                            $scope.asset.rma_rule_id = '';
                            $scope.asset.rule_description = '';
                            $scope.asset.rule_id_text  = '';
                            //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };


        $scope.GetMPNFromSerialRMA = function () {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'audit/includes/rma_investigation_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetMPNFromSerialRMA&SerialNumber='+$scope.asset.SerialNumber+'&CustomPalletID='+$scope.CustomPalletID,
                success: function(data) {
                    if(data.Success) {
                        $scope.asset.UniversalModelNumber = data.Result.UniversalModelNumber;
                        $scope.GetCurrentTime($scope.asset,'mpn_scan_time');
                        $scope.asset.AssetScanID = data.Result.AssetScanID;


                        $scope.asset.parttypeid = data.Result.parttypeid;
                        $scope.asset.COOID = data.Result.COOID;

                        $scope.GetCurrentTime($scope.asset,'part_type_scan_time');
                        $scope.GetCurrentTime($scope.asset,'coo_scan_time');
                        if(data.Result.parttypeid > 0 && data.Result.parttypeid > 0) {
                            $scope.ApplyBusinessRule();
                        } else {
                            if(data.Result.parttypeid == '' || data.Result.parttypeid == null) {
                                $window.document.getElementById('parttypeid').focus();
                            } else if(data.Result.COOID == '' || data.Result.COOID == null) {
                                $window.document.getElementById('COOID').focus();
                            }
                        }

                        if(data.Result.Warranty.warranty_id) {
                            $scope.asset.rack_asset_id = data.Result.Warranty.rack_asset_id;
                            $scope.asset.host_id = data.Result.Warranty.host_id;
                            $scope.asset.host_asset_id = data.Result.Warranty.host_asset_id;
                            $scope.asset.in_warranty = data.Result.Warranty.in_warranty;
                            $scope.asset.warranty_expiration_date = data.Result.Warranty.warranty_expiration_date;
                        } else {
                            $scope.asset.rack_asset_id = 'n/a';
                            $scope.asset.host_id = 'n/a';
                            $scope.asset.host_asset_id = 'n/a';
                            $scope.asset.in_warranty = 'No';
                            $scope.asset.warranty_expiration_date = '';
                        }

                        $scope.asset.ManufacturerSerialNumber = data.Result.SerialNumber;

                        $scope.ApplyBusinessRule();
                    } else {
                        $scope.asset.UniversalModelNumber = '';
                        $scope.asset.AssetScanID = '';
                        $scope.asset.parttypeid = '';
                        $scope.asset.COOID = '';
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        //$scope.messages.push( {'message' : data.Result,'show': true,'type': 'danger','icon': 'error','message_type': 'Error'});
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.SerialChanged = function () {
            $scope.asset.AssetScanID = '';
            $scope.asset.UniversalModelNumber = '';
            $scope.parttypeid = '';
            $scope.COOID = '';
            $scope.asset.disposition_id = '';
            $scope.asset.disposition = '';
            $scope.disposition_color = '';
            $scope.asset.CustomPalletID = '';
            $scope.asset.BinName = '';
            $scope.asset.rma_rule_id = '';
            $scope.asset.rule_description = '';
            $scope.asset.rule_id_text  = '';
            $scope.asset.rack_asset_id = '';
            $scope.asset.host_id = '';
            $scope.asset.host_asset_id = '';
            $scope.asset.ManufacturerSerialNumber = '';

            $scope.asset.rack_asset_id = 'n/a';
            $scope.asset.host_id = 'n/a';
            $scope.asset.host_asset_id = 'n/a';
            $scope.asset.in_warranty = 'No';
            $scope.asset.warranty_expiration_date = '';
        };

        $scope.MPNChanged = function () {
            $scope.asset.disposition_id = '';
            $scope.asset.disposition = '';
            $scope.disposition_color = '';
            $scope.parttypeid = '';
            $scope.COOID = '';

            $scope.asset.CustomPalletID = '';
            $scope.asset.BinName = '';
            $scope.asset.rma_rule_id = '';
            $scope.asset.rule_description = '';
            $scope.asset.rule_id_text  = '';
            $scope.asset.rma_custom_id = 'n/a';
        };

        $scope.UpdateAssetRMA = function (ev) {


            if($scope.asset.CloseBin) {

                var confirm = $mdDialog.confirm()
                .title('Are you sure, You want to Empty BIN ?')
                .content('')
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Empty BIN')
                .cancel('No');
                $mdDialog.show(confirm).then(function() {
                    $scope.asset.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'audit/includes/rma_investigation_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=UpdateAssetRMA&'+$.param($scope.asset)+'&SiteID='+$scope.SiteID+'&FromCustomPalletID='+$scope.CustomPalletID+'&CloseBin=1',
                        success: function(data){
                            if(data.Success) {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                if(data.Asset) {
                                    if($scope.StationCustomPallets.length > 0) {
                                        for(var i=0;i<$scope.StationCustomPallets.length;i++) {
                                            if($scope.StationCustomPallets[i].CustomPalletID == data.Asset.CustomPalletID) {
                                                $scope.StationCustomPallets[i].AssetsCount = data.Asset.AssetsCount;
                                            }
                                        }
                                    }
                                    if($scope.Assets.length == 0) {
                                        $scope.CallServerFunction(0);
                                    } else {
                                        $scope.Assets.unshift(data.Asset);
                                    }
                                }
                                if(data.CloseMessage) {
                                    $mdToast.show (
                                        $mdToast.simple()
                                            .content(data.CloseMessage)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-info md-block')
                                    );
                                }
                                if(data.BinClosed == '1') {
                                    $scope.CustomPalletID = '';
                                    $scope.BinName = '';
                                    $scope.Assets = [];
                                }
                                $scope.ClearAsset();
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }
                    });
                }, function() {
                    $scope.asset.CloseBin = false;
                    $scope.asset.busy = true;
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'audit/includes/rma_investigation_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=UpdateAssetRMA&'+$.param($scope.asset)+'&SiteID='+$scope.SiteID+'&FromCustomPalletID='+$scope.CustomPalletID,
                        success: function(data){
                            if(data.Success) {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                if(data.Asset) {
                                    if($scope.StationCustomPallets.length > 0) {
                                        for(var i=0;i<$scope.StationCustomPallets.length;i++) {
                                            if($scope.StationCustomPallets[i].CustomPalletID == data.Asset.CustomPalletID) {
                                                $scope.StationCustomPallets[i].AssetsCount = data.Asset.AssetsCount;
                                            }
                                        }
                                    }
                                    if($scope.Assets.length == 0) {
                                        $scope.CallServerFunction(0);
                                    } else {
                                        $scope.Assets.unshift(data.Asset);
                                    }
                                }

                                if(data.CloseMessage) {
                                    $mdToast.show (
                                        $mdToast.simple()
                                            .content(data.CloseMessage)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-info md-block')
                                    );
                                }

                                $scope.ClearAsset();
                                $window.document.getElementById('SerialNumber').focus();
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {
                            $scope.asset.busy = false;
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }
                    });

                });

            } else {
                $scope.asset.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/rma_investigation_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=UpdateAssetRMA&'+$.param($scope.asset)+'&SiteID='+$scope.SiteID+'&FromCustomPalletID='+$scope.CustomPalletID,
                    success: function(data){
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            if(data.Asset) {
                                if($scope.StationCustomPallets.length > 0) {
                                    for(var i=0;i<$scope.StationCustomPallets.length;i++) {
                                        if($scope.StationCustomPallets[i].CustomPalletID == data.Asset.CustomPalletID) {
                                            $scope.StationCustomPallets[i].AssetsCount = data.Asset.AssetsCount;
                                        }
                                    }
                                }
                                if($scope.Assets.length == 0) {
                                    $scope.CallServerFunction(0);
                                } else {
                                    $scope.Assets.unshift(data.Asset);
                                }
                            }
                            if(data.CloseMessage) {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.CloseMessage)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-info md-block')
                                );
                            }
                            $scope.ClearAsset();
                            $window.document.getElementById('SerialNumber').focus();
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $scope.asset.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.asset.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            }
        };

        $scope.GetMPNPartTypeDetails = function (asset) {
            if(asset.UniversalModelNumber != '') {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/rma_investigation_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetMPNPartTypeDetails&UniversalModelNumber='+asset.UniversalModelNumber,
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            asset.parttypeid = data.parttypeid;
                            asset.COOID = data.COOID;
                            $scope.ApplyBusinessRule();
                        } else {
                            asset.parttypeid = '';
                            asset.COOID = '';                      
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );                            
                        }                        
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });


            }
        };

        $scope.ClearAsset = function () {
            //$scope.asset = {'rma_input_id' : $scope.DefaultInputID,'RigID' : $scope.DefaultRigID};
            $scope.asset.SerialNumber = '';
            $scope.asset.AssetScanID = '';
            $scope.asset.UniversalModelNumber = '';
            $scope.asset.parttypeid = '';
            $scope.asset.COOID = '';
            $scope.asset.disposition_id = '';
            $scope.asset.disposition = '';
            $scope.disposition_color = '';
            $scope.asset.CustomPalletID = '';
            $scope.asset.BinName = '';
            $scope.asset.rma_rule_id = '';
            $scope.asset.rule_description = '';
            $scope.asset.rule_id_text  = '';
            if($scope.CopyCustomID) {

            } else {
                $scope.asset.rma_custom_id = 'n/a';
            }
            $scope.asset.rma_notes = 'n/a';
            $scope.asset.ManufacturerSerialNumber = '';


            $scope.asset.rack_asset_id = 'n/a';
            $scope.asset.host_id = 'n/a';
            $scope.asset.host_asset_id = 'n/a';
            $scope.asset.in_warranty = 'No';
            $scope.asset.warranty_expiration_date = '';
        };


        function MoveBinTPVRController($scope,$mdDialog,CurrentPallet,$mdToast,$window) {
            $scope.CurrentPallet = CurrentPallet;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails);
            };
            $scope.focusNextField = function (id) {
                $window.document.getElementById(id).focus();                
            };

            $scope.MoveBinToNewLocationGroup = function (ev) {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MoveBinToNewLocationGroup&'+$.param($scope.confirmDetails)+'&'+$.param($scope.CurrentPallet),
                    success: function(data) {
                        if(data.Success) {
                            // $mdToast.show (
                            //     $mdToast.simple()
                            //     .content(data.Result)
                            //     .action('OK')
                            //     .position('right')
                            //     .hideDelay(0)
                            //     .toastClass('md-toast-success md-block')
                            // );   
                            
                            var message = 'Location Group Updated : Sub Location (<b>'+data.newLocationName+'</b>) from  Location Group (<b>'+data.GroupName+'</b>) has been assigned to BIN (<b>'+$scope.CurrentPallet.BinName+'</b>)';
    
                            $mdToast.show({
                                template: `
                                    <md-toast class="md-toast-success md-block">
                                        <span class="md-toast-text" flex>${message}</span>
                                        <md-button class="md-highlight" ng-click="closeToast()">OK</md-button>
                                    </md-toast>
                                `,
                                controller: function($scope, $mdToast) {
                                    $scope.closeToast = function() {
                                        $mdToast.hide();
                                    };
                                },
                                hideDelay: 0,
                                position: 'right',
                                toastClass: 'md-toast-success md-block'
                            });
                            
                            
                            $scope.hide();
                        } else {                                                        
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        //alert(data.Result);
                        //alert("3");
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
                
            };


            function LocationChange1(text) {
                $scope.confirmDetails.group = text;
            }
    
            function selectedLocationChange1(item) {
                if (item) {
                    if (item.value) {
                        $scope.confirmDetails.group = item.value;
                    } else {
                        $scope.confirmDetails.group = '';
                    }
                } else {
                    $scope.confirmDetails.group = '';
                }
            }
    
            $scope.queryLocationSearch1 = queryLocationSearch1;
            $scope.LocationChange1 = LocationChange1;
            $scope.selectedLocationChange1 = selectedLocationChange1;
            function queryLocationSearch1(query) {
                if (query) {
                    if (query != '' && query != 'undefined') {                    
                        return $http.get(host + 'receive/includes/receive_submit.php?ajax=GetMatchingLocationGroups&keyword=' + query + '&LocationType=WIP')
                            .then(function (res) {
                                if (res.data.Success == true) {
                                    if (res.data.Result.length > 0) {
                                        var result_array = [];
                                        for (var i = 0; i < res.data.Result.length; i++) {
                                            result_array.push({ value: res.data.Result[i]['GroupName'], GroupName: res.data.Result[i]['GroupName'] });
                                        }
                                        return result_array;
                                    } else {
                                        return [];
                                    }
                                } else {
                                    return [];
                                }
                            });
                    } else {
                        return [];
                    }
                } else {
                    return [];
                }
            }


        }

        $scope.CurrentPallet = {};
        $scope.confirmDetails = {};
        function afterShowAnimation () {            
            $window.document.getElementById("AuditController").focus();            
        }

        $scope.MoveBinToStationLocationGroup = function(bin,SiteID,ev) {
            bin.SiteID = SiteID;
             //$scope.CurrentPallet = pallet;
            //$scope.asset.PasswordVerified = false;
            $mdDialog.show({
                controller: MoveBinTPVRController,
                templateUrl: 'password.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation,
                clickOutsideToClose:true,
                resolve: {
                    CurrentPallet: function () {
                        return bin;
                    }
                }
            })
            .then(function(confirmDetails) {  
                $scope.GetStationCustomPallets();
            }, function(confirmDetails) {
                $scope.confirmDetails = confirmDetails;
            });

        };


        //Start TPVR for Consolidation

        function ConsolidationTPVRController($scope,$mdDialog,CurrentCustomPalletPallet,$mdToast,$window) {
            $scope.CurrentCustomPalletPallet = CurrentCustomPalletPallet;
            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails3);
            };
            $scope.cancel = function() {
                $mdDialog.cancel($scope.confirmDetails3);
            };
            $scope.focusNextField = function (id) {
                $window.document.getElementById(id).focus();                
            };
  
            $scope.ConsolidateBin = function (ev) {
              console.log($scope.confirmDetails4);
              console.log($scope.CurrentCustomPalletPallet);
              $rootScope.$broadcast('preloader:active');
  
              jQuery.ajax({
                  url: host+'recovery/includes/recovery_submit.php',
                  dataType: 'json',
                  type: 'post',
                  data: 'ajax=ConsolidateBin&FromBinName='+$scope.CurrentCustomPalletPallet.BinName+'&ToBinName='+$scope.confirmDetails4.ToBinName+'&FromCustomPalletID='+$scope.CurrentCustomPalletPallet.CustomPalletID+'&ToShipmentContainer='+$scope.confirmDetails4.ToShipmentContainer+'&ModuleName=RMA Investigation',
                  success: function(data) {
                      $rootScope.$broadcast('preloader:hide');
                      if(data.Success) {
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-success md-block')
                          );      
                          $scope.hide();
                      } else {                                                        
                          $mdToast.show (
                              $mdToast.simple()
                              .content(data.Result)
                              .action('OK')
                              .position('right')
                              .hideDelay(0)
                              .toastClass('md-toast-danger md-block')
                          );
                      }                    
                      initSessionTime(); $scope.$apply();
                  }, error : function (data) {
                      //alert(data.Result);
                      //alert("3");
                      $scope.error = data;
                      initSessionTime(); $scope.$apply();
                  }
              });
  
            };
            $scope.MoveBinToNewLocationGroup = function (ev) {
                
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'audit/includes/audit_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=MoveBinToNewLocationGroup&'+$.param($scope.confirmDetails3)+'&'+$.param($scope.CurrentPallet),
                    success: function(data) {
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                            );      
                            $scope.hide();
                        } else {                                                        
                            $mdToast.show (
                                $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        //alert(data.Result);
                        //alert("3");
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
                
            };
          }
  
        $scope.CurrentCustomPalletPallet = {};
        $scope.confirmDetails4 = {};
        function afterShowAnimation14 () {            
          $window.document.getElementById("newbin").focus();            
        }
  
        $scope.ConsolidateBin = function(bin,SiteID,ev) {
          console.log(bin);
            bin.SiteID = SiteID;
            //$scope.CurrentPallet = pallet;
            //$scope.asset.PasswordVerified = false;
            $mdDialog.show({
                controller: ConsolidationTPVRController,
                templateUrl: 'password4.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                onComplete: afterShowAnimation14,
                clickOutsideToClose:true,
                resolve: {
                    CurrentCustomPalletPallet: function () {
                        return bin;
                    }
                }
            })
            .then(function(confirmDetails4) {  
                $scope.GetStationCustomPallets();
            }, function(confirmDetails4) {
                $scope.confirmDetails4 = confirmDetails4;
            });
  
        };
  
        //End TPVR for Consolidation

        // Actions Functions for Bin Management

        // Create Bin Function
        $scope.CreateBin = function(bin, SiteID, ev) {
            // Store the current row disposition for later use in MapCustomPalletToDisposition
            $scope.currentRowDisposition = {
                disposition_id: bin.disposition_id,
                disposition: bin.disposition
            };

            $scope.createBinData = {
                idPackage: '',
                BinName: '',
                FacilityID: $scope.SessionFacility ? $scope.SessionFacility.FacilityID : '',
                LocationType: 'WIP',
                LocationGroup: '',
                Disposition: bin.disposition || '',
                Notes: ''
            };
            $scope.createBinBusy = false;

            // Get station location info to populate LocationGroup
            if (SiteID) {
                $scope.getStationLocationInfo(SiteID);
            }

            // Load package types
            if ($scope.createBinData.FacilityID) {
                $scope.GetBinPackageTypesForCreateBin();
            }

            $mdDialog.show({
                templateUrl: 'createBinModal.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                scope: $scope,
                preserveScope: true
            });
        };

        $scope.createBin = function() {
            $scope.createBinBusy = true;
            $rootScope.$broadcast('preloader:active');

            // Prepare data for bin creation (matching PartsRecovery structure)
            var binData = {
                ajax: 'CreateBin',
                BinName: $scope.createBinData.BinName,
                idPackage: $scope.createBinData.idPackage,
                FacilityID: $scope.createBinData.FacilityID,
                LocationID: $scope.createBinData.LocationID,
                LocationGroupID: $scope.createBinData.LocationGroupID,
                Notes: $scope.createBinData.Notes
            };

            jQuery.ajax({
                url: host + 'audit/includes/rma_investigation_submit.php',
                dataType: 'json',
                type: 'post',
                data: binData,
                success: function(data) {
                    $scope.createBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if(data.Success) {
                        // After successful bin creation, automatically assign it to disposition
                        // Create proper object for MapCustomPalletToDisposition (matching PartsRecovery pattern)
                        var newBinItem = {
                            BinName: binData.BinName,
                            CustomPalletID: null,
                            disposition_id: $scope.currentRowDisposition.disposition_id  // Use disposition from the row where Create Bin was clicked
                        };

                        // Call MapCustomPalletToDisposition to assign the newly created bin to station
                        $scope.MapCustomPalletToDisposition(newBinItem);

                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        $mdDialog.hide();
                        $scope.GetStationCustomPallets();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(data) {
                    $scope.createBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error creating bin')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };

        $scope.cancel = function() {
            $mdDialog.cancel();
        };

        // Handle bin type change in create bin modal
        $scope.onBinTypeChange = function() {
            // This function can be used to handle any logic when bin type changes
            // Currently no specific logic needed, but keeping for consistency with failure analysis
        };

        // Close Bin functionality (same as admin module)
        function CloseBinControllerRecovery($scope, $mdDialog, CurrentBin, $window) {
            $scope.CurrentBin = CurrentBin;
            $scope.confirmDetails = {};

            $scope.hide = function() {
                $mdDialog.hide($scope.confirmDetails);
            };
            $scope.cancel = function() {
                $mdDialog.cancel();
            };

            $scope.FocusNextField = function (nextid, wait) {
                if(wait == '1') {
                    setTimeout(function () {
                        $window.document.getElementById(nextid).focus();
                    }, 100);
                } else {
                    $window.document.getElementById(nextid).focus();
                }
            };

            // Focus on first field when modal opens
            setTimeout(function() {
                $window.document.getElementById("AuditController").focus();
            }, 100);
        }

        // Close Bin Function
        $scope.CloseBin = function(bin, siteID, $event) {
            $event.preventDefault();
            $event.stopPropagation();

            // Validate that bin is not inside a parent
            if (bin.ParentBinName && bin.ParentBinName.trim() !== '') {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Bin cannot be closed while it is inside a parent bin. Please remove from parent first.')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
                return;
            }

            $mdDialog.show({
                controller: CloseBinControllerRecovery,
                templateUrl: 'closeBinModal.html',
                parent: angular.element(document.body),
                targetEvent: $event,
                clickOutsideToClose: true,
                resolve: {
                    CurrentBin: function () {
                        return bin;
                    }
                }
            })
            .then(function(confirmDetails) {
                // Process close bin with TPVR details
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host + 'audit/includes/rma_investigation_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CloseBin&' + $.param(confirmDetails) + '&CustomPalletID=' + bin.CustomPalletID + '&SiteID=' + siteID,
                    success: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            $scope.GetStationCustomPallets();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        initSessionTime();
                        $scope.$apply();
                    },
                    error: function(data) {
                        $rootScope.$broadcast('preloader:hide');
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Error closing bin')
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                        initSessionTime();
                        $scope.$apply();
                    }
                });
            }, function() {
                // User cancelled
            });
        };

        // Nest to Bin Function
        $scope.NestToBin = function(bin, SiteID, ev) {
            $scope.nestToBinData = {
                BinName: bin.BinName,
                CustomPalletID: bin.CustomPalletID,
                parentBin: ''
            };
            $scope.nestToBinBusy = false;

            $mdDialog.show({
                templateUrl: 'nestToBinModal.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                scope: $scope,
                preserveScope: true
            });
        };

        $scope.nestToBin = function() {
            $scope.nestToBinBusy = true;
            $rootScope.$broadcast('preloader:active');

            jQuery.ajax({
                url: host + 'audit/includes/rma_investigation_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=NestToBin&' + $.param($scope.nestToBinData) + '&SiteID=' + $scope.SiteID,
                success: function(data) {
                    $scope.nestToBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');

                    if(data.Success) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-success md-block')
                        );
                        $mdDialog.hide();
                        $scope.GetStationCustomPallets();
                    } else {
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime();
                    $scope.$apply();
                },
                error: function(data) {
                    $scope.nestToBinBusy = false;
                    $rootScope.$broadcast('preloader:hide');
                    $mdToast.show(
                        $mdToast.simple()
                            .content('Error nesting bin')
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                    initSessionTime();
                    $scope.$apply();
                }
            });
        };


    });

})();
